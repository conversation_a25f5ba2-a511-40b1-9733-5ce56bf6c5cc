"""
خدمة إرسال البريد الإلكتروني مع دعم المتغيرات الديناميكية
"""

import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.base import MIMEBase
from email import encoders
from django.core.mail import EmailMultiAlternatives
from django.conf import settings
from django.utils import timezone
from django.template import Template, Context
from .email_models import EmailSettings, EmailTemplate, EmailQueue, EmailLog, EmailSubscription
from .models import AcademySettings

logger = logging.getLogger(__name__)


class EmailService:
    """خدمة إرسال البريد الإلكتروني الموحدة"""
    
    def __init__(self):
        self.email_settings = EmailSettings.get_active_settings()
        self.academy_settings = AcademySettings.get_settings()
    
    def get_academy_variables(self):
        """الحصول على متغيرات الأكاديمية"""
        return {
            'academy_name': self.academy_settings.academy_name,
            'academy_logo': self.academy_settings.academy_logo.url if self.academy_settings.academy_logo else '',
            'academy_slogan': self.academy_settings.academy_slogan or '',
            'academy_email': self.academy_settings.academy_email,
            'academy_phone': self.academy_settings.academy_phone or '',
            'academy_whatsapp': self.academy_settings.academy_whatsapp or '',
            'academy_address': self.academy_settings.academy_address or '',
            'academy_website': self.academy_settings.academy_website or '',
            'academy_description': self.academy_settings.academy_description or '',
            'academy_working_hours': self.academy_settings.academy_working_hours or '',
            'current_year': timezone.now().year,
            'current_date': timezone.now().strftime('%Y-%m-%d'),
        }
    
    def render_template(self, template, context=None):
        """عرض القالب مع المتغيرات"""
        if context is None:
            context = {}
        
        # إضافة متغيرات الأكاديمية
        academy_vars = self.get_academy_variables()
        context.update(academy_vars)
        
        # عرض المحتوى
        try:
            html_template = Template(template.html_content)
            subject_template = Template(template.subject)
            
            rendered_html = html_template.render(Context(context))
            rendered_subject = subject_template.render(Context(context))
            
            return {
                'subject': rendered_subject,
                'html_content': rendered_html,
                'text_content': template.text_content or '',
            }
        except Exception as e:
            logger.error(f"خطأ في عرض القالب {template.name}: {str(e)}")
            return None
    
    def send_email(self, recipient, template, context=None, immediate=True):
        """إرسال بريد إلكتروني"""
        try:
            # التحقق من إعدادات البريد
            if not self.email_settings:
                logger.error("لا توجد إعدادات بريد إلكتروني نشطة")
                return False
            
            # التحقق من اشتراك المستخدم
            subscription = EmailSubscription.get_or_create_for_user(recipient)
            if not subscription.email_notifications_enabled:
                logger.info(f"إشعارات البريد معطلة للمستخدم {recipient.email}")
                return False
            
            # عرض القالب
            rendered = self.render_template(template, context)
            if not rendered:
                return False
            
            if immediate:
                return self._send_immediate(recipient, rendered)
            else:
                return self._queue_email(recipient, template, rendered, context)
                
        except Exception as e:
            logger.error(f"خطأ في إرسال البريد إلى {recipient.email}: {str(e)}")
            return False
    
    def _send_immediate(self, recipient, rendered_content):
        """إرسال فوري للبريد الإلكتروني باستخدام SMTP مخصص"""
        try:
            # إنشاء الرسالة
            msg = MIMEMultipart('alternative')
            msg['Subject'] = rendered_content['subject']
            msg['From'] = f"{self.email_settings.from_name} <{self.email_settings.from_email}>"
            msg['To'] = recipient.email

            # إضافة المحتوى النصي
            if rendered_content['text_content']:
                text_part = MIMEText(rendered_content['text_content'], 'plain', 'utf-8')
                msg.attach(text_part)

            # إضافة المحتوى HTML
            if rendered_content['html_content']:
                html_part = MIMEText(rendered_content['html_content'], 'html', 'utf-8')
                msg.attach(html_part)

            # إرسال الرسالة باستخدام SMTP مخصص
            success = self._send_via_smtp(msg, recipient.email)

            # تسجيل النتيجة
            EmailLog.objects.create(
                recipient=recipient,
                subject=rendered_content['subject'],
                status='sent' if success else 'failed',
                sent_at=timezone.now(),
                error_message='' if success else 'فشل في الإرسال'
            )

            return success

        except Exception as e:
            # تسجيل الخطأ
            logger.error(f"خطأ في إرسال البريد إلى {recipient.email}: {str(e)}")

            # تسجيل الخطأ في قاعدة البيانات
            EmailLog.objects.create(
                recipient=recipient,
                subject=rendered_content['subject'],
                status='failed',
                sent_at=timezone.now(),
                error_message=str(e)
            )

            return False

    def _send_via_smtp(self, msg, recipient_email):
        """إرسال الرسالة باستخدام SMTP مخصص"""
        try:
            # إنشاء اتصال SMTP
            if self.email_settings.use_ssl:
                server = smtplib.SMTP_SSL(
                    self.email_settings.smtp_host,
                    self.email_settings.smtp_port,
                    timeout=30
                )
            else:
                server = smtplib.SMTP(
                    self.email_settings.smtp_host,
                    self.email_settings.smtp_port,
                    timeout=30
                )

                if self.email_settings.use_tls:
                    server.starttls()

            # تسجيل الدخول
            server.login(
                self.email_settings.smtp_username,
                self.email_settings.decrypt_password()
            )

            # إرسال الرسالة
            server.send_message(msg)
            server.quit()

            logger.info(f"تم إرسال البريد بنجاح إلى {recipient_email}")
            return True

        except Exception as e:
            logger.error(f"خطأ في إرسال البريد عبر SMTP إلى {recipient_email}: {str(e)}")
            return False
    
    def _queue_email(self, recipient, template, rendered_content, context, delay_minutes=0):
        """إضافة البريد إلى طابور الإرسال"""
        try:
            scheduled_at = timezone.now()
            if delay_minutes > 0:
                from datetime import timedelta
                scheduled_at += timedelta(minutes=delay_minutes)
            
            EmailQueue.objects.create(
                recipient=recipient,
                template=template,
                subject=rendered_content['subject'],
                context_data=context or {},
                scheduled_at=scheduled_at,
                status='pending'
            )
            
            logger.info(f"تم إضافة البريد إلى الطابور للمستخدم {recipient.email}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إضافة البريد للطابور: {str(e)}")
            return False
    
    def process_queue(self, limit=50):
        """معالجة طابور البريد الإلكتروني"""
        try:
            # الحصول على الرسائل المعلقة
            pending_emails = EmailQueue.objects.filter(
                status='pending',
                scheduled_at__lte=timezone.now()
            ).order_by('scheduled_at')[:limit]
            
            sent_count = 0
            failed_count = 0
            
            for email_item in pending_emails:
                try:
                    # تحديث الحالة
                    email_item.status = 'sending'
                    email_item.save()
                    
                    # عرض القالب
                    rendered = self.render_template(email_item.template, email_item.context_data)
                    if not rendered:
                        email_item.status = 'failed'
                        email_item.error_message = 'فشل في عرض القالب'
                        email_item.attempts += 1
                        email_item.save()
                        failed_count += 1
                        continue
                    
                    # إرسال البريد
                    success = self._send_immediate(email_item.recipient, rendered)
                    
                    if success:
                        email_item.status = 'sent'
                        sent_count += 1
                    else:
                        email_item.status = 'failed'
                        email_item.attempts += 1
                        failed_count += 1
                    
                    email_item.save()
                    
                except Exception as e:
                    email_item.status = 'failed'
                    email_item.error_message = str(e)
                    email_item.attempts += 1
                    email_item.save()
                    failed_count += 1
                    logger.error(f"خطأ في معالجة البريد {email_item.id}: {str(e)}")
            
            logger.info(f"تمت معالجة الطابور: {sent_count} نجح، {failed_count} فشل")
            return {'sent': sent_count, 'failed': failed_count}
            
        except Exception as e:
            logger.error(f"خطأ في معالجة طابور البريد: {str(e)}")
            return {'sent': 0, 'failed': 0}
    
    def test_connection(self):
        """اختبار الاتصال بخادم SMTP"""
        try:
            if not self.email_settings:
                return False, "لا توجد إعدادات بريد إلكتروني"
            
            # اختبار الاتصال
            if self.email_settings.use_ssl:
                server = smtplib.SMTP_SSL(self.email_settings.smtp_host, self.email_settings.smtp_port)
            else:
                server = smtplib.SMTP(self.email_settings.smtp_host, self.email_settings.smtp_port)
                if self.email_settings.use_tls:
                    server.starttls()
            
            # تسجيل الدخول
            password = self.email_settings.decrypt_password()
            server.login(self.email_settings.smtp_username, password)
            server.quit()
            
            return True, "تم الاتصال بنجاح"
            
        except Exception as e:
            return False, f"فشل الاتصال: {str(e)}"
    
    def get_statistics(self):
        """الحصول على إحصائيات البريد الإلكتروني"""
        try:
            today = timezone.now().date()
            
            # إحصائيات اليوم
            today_sent = EmailLog.objects.filter(
                sent_at__date=today,
                status='sent'
            ).count()
            
            today_failed = EmailLog.objects.filter(
                sent_at__date=today,
                status='failed'
            ).count()
            
            # طابور الانتظار
            pending_queue = EmailQueue.objects.filter(status='pending').count()
            
            # إجمالي الرسائل
            total_sent = EmailLog.objects.filter(status='sent').count()
            total_failed = EmailLog.objects.filter(status='failed').count()
            
            return {
                'today_sent': today_sent,
                'today_failed': today_failed,
                'pending_queue': pending_queue,
                'total_sent': total_sent,
                'total_failed': total_failed,
                'success_rate': round((total_sent / (total_sent + total_failed) * 100), 2) if (total_sent + total_failed) > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات: {str(e)}")
            return {
                'today_sent': 0,
                'today_failed': 0,
                'pending_queue': 0,
                'total_sent': 0,
                'total_failed': 0,
                'success_rate': 0
            }
