"""
خدمة إرسال البريد الإلكتروني مع دعم المتغيرات الديناميكية
"""

import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders
from django.core.mail import EmailMultiAlternatives
from django.conf import settings
from django.utils import timezone
from django.template import Template, Context
from .email_models import EmailSettings, EmailTemplate, EmailQueue, EmailLog, EmailSubscription
from .models import AcademySettings

logger = logging.getLogger(__name__)


class EmailService:
    """خدمة إرسال البريد الإلكتروني الموحدة"""
    
    def __init__(self):
        self.email_settings = EmailSettings.get_active_settings()
        self.academy_settings = AcademySettings.get_settings()
    
    def get_site_domain(self, request=None):
        """الحصول على النطاق الحالي للموقع"""
        from django.conf import settings

        # إذا كان هناك request، استخدم get_host()
        if request:
            protocol = 'https' if request.is_secure() else 'http'
            return f"{protocol}://{request.get_host()}"

        # إذا لم يكن هناك request، استخدم الإعدادات
        if hasattr(settings, 'SITE_DOMAIN') and settings.SITE_DOMAIN:
            return settings.SITE_DOMAIN

        # الافتراضي للتطوير
        return 'http://localhost:8080'

    def get_dashboard_urls(self, user_role, site_domain=None):
        """الحصول على روابط لوحة التحكم لكل دور"""
        if not site_domain:
            site_domain = self.get_site_domain()

        base_urls = {
            'admin': f'{site_domain}/dashboard/admin/',
            'teacher': f'{site_domain}/dashboard/teacher/',
            'student': f'{site_domain}/dashboard/student/'
        }

        # الروابط المحددة لكل دور
        role_specific_urls = {
            'admin': {
                'dashboard': base_urls['admin'],
                'users': f'{base_urls["admin"]}users/',
                'subscriptions': f'{base_urls["admin"]}subscriptions/',
                'lessons': f'{base_urls["admin"]}lessons/',
                'reports': f'{base_urls["admin"]}reports/',
                'ratings': f'{base_urls["admin"]}ratings/',
                'payments': f'{base_urls["admin"]}payment-management/',
                'email_settings': f'{base_urls["admin"]}email-settings/',
            },
            'teacher': {
                'dashboard': base_urls['teacher'],
                'lessons': f'{base_urls["teacher"]}lessons/',
                'students': f'{base_urls["teacher"]}students/',
                'schedule': f'{base_urls["teacher"]}schedule/',
                'ratings': f'{base_urls["teacher"]}ratings/',
                'earnings': f'{base_urls["teacher"]}earnings/',
                'reports': f'{base_urls["teacher"]}reports/',
            },
            'student': {
                'dashboard': base_urls['student'],
                'lessons': f'{base_urls["student"]}lessons/',
                'subscriptions': f'{base_urls["student"]}subscriptions/',
                'courses': f'{base_urls["student"]}courses/',
                'progress': f'{base_urls["student"]}progress/',
                'payments': f'{base_urls["student"]}payments/',
                'archive': f'{base_urls["student"]}archive/',
            }
        }

        # إضافة الروابط العامة
        common_urls = {
            'login': f'{site_domain}/login/',
            'logout': f'{site_domain}/logout/',
            'profile': f'{site_domain}/dashboard/profile/',
            'support': f'{site_domain}/support/',
            'notifications': f'{site_domain}/notifications/',
        }

        result = role_specific_urls.get(user_role, {})
        result.update(common_urls)
        return result

    def get_academy_variables(self, request=None):
        """الحصول على متغيرات الأكاديمية مع الروابط الديناميكية"""
        site_domain = self.get_site_domain(request)

        return {
            'academy_name': self.academy_settings.academy_name,
            'academy_logo': self.academy_settings.academy_logo.url if self.academy_settings.academy_logo else '',
            'academy_slogan': self.academy_settings.academy_slogan or '',
            'academy_email': self.academy_settings.academy_email,
            'academy_phone': self.academy_settings.academy_phone or '',
            'academy_whatsapp': self.academy_settings.academy_whatsapp or '',
            'academy_address': self.academy_settings.academy_address or '',
            'academy_website': self.academy_settings.academy_website or '',
            'academy_description': self.academy_settings.academy_description or '',
            'academy_working_hours': self.academy_settings.academy_working_hours or '',
            'current_year': timezone.now().year,
            'current_date': timezone.now().strftime('%Y-%m-%d'),
            'current_time': timezone.now().strftime('%H:%M'),
            'site_domain': site_domain,
            'login_url': f'{site_domain}/login/',
            'dashboard_url': f'{site_domain}/dashboard/',
        }
    
    def render_template(self, template, context=None, request=None, user_role=None):
        """عرض القالب مع المتغيرات والروابط الديناميكية"""
        if context is None:
            context = {}

        # إضافة متغيرات الأكاديمية مع الروابط الديناميكية
        academy_vars = self.get_academy_variables(request)
        context.update(academy_vars)

        # إضافة روابط لوحة التحكم إذا تم تحديد دور المستخدم
        if user_role:
            dashboard_urls = self.get_dashboard_urls(user_role, academy_vars['site_domain'])
            context.update(dashboard_urls)
        
        # عرض المحتوى
        try:
            html_template = Template(template.html_content)
            subject_template = Template(template.subject)
            
            rendered_html = html_template.render(Context(context))
            rendered_subject = subject_template.render(Context(context))
            
            return {
                'subject': rendered_subject,
                'html_content': rendered_html,
                'text_content': template.text_content or '',
            }
        except Exception as e:
            logger.error(f"خطأ في عرض القالب {template.name}: {str(e)}")
            return None
    
    def send_email(self, recipient, template, context=None, immediate=True, request=None):
        """إرسال بريد إلكتروني مع دعم الروابط الديناميكية"""
        try:
            # التحقق من إعدادات البريد
            if not self.email_settings:
                logger.error("لا توجد إعدادات بريد إلكتروني نشطة")
                return False

            # التحقق من اشتراك المستخدم
            subscription = EmailSubscription.get_or_create_for_user(recipient)
            if not subscription.email_notifications_enabled:
                logger.info(f"إشعارات البريد معطلة للمستخدم {recipient.email}")
                return False

            # تحديد دور المستخدم
            user_role = None
            if hasattr(recipient, 'user_type'):
                user_role = recipient.user_type
            elif hasattr(recipient, 'is_admin') and recipient.is_admin():
                user_role = 'admin'
            elif hasattr(recipient, 'is_teacher') and recipient.is_teacher():
                user_role = 'teacher'
            elif hasattr(recipient, 'is_student') and recipient.is_student():
                user_role = 'student'

            # عرض القالب مع الروابط الديناميكية
            rendered = self.render_template(template, context, request, user_role)
            if not rendered:
                return False

            if immediate:
                return self._send_immediate(recipient, rendered)
            else:
                return self._queue_email(recipient, template, rendered, context)

        except Exception as e:
            logger.error(f"خطأ في إرسال البريد إلى {recipient.email}: {str(e)}")
            return False
    
    def _send_immediate(self, recipient, rendered_content):
        """إرسال فوري للبريد الإلكتروني باستخدام SMTP مخصص"""
        try:
            # إنشاء الرسالة
            msg = MIMEMultipart('alternative')
            msg['Subject'] = rendered_content['subject']
            msg['From'] = f"{self.email_settings.from_name} <{self.email_settings.from_email}>"
            msg['To'] = recipient.email

            # إضافة المحتوى النصي
            if rendered_content['text_content']:
                text_part = MIMEText(rendered_content['text_content'], 'plain', 'utf-8')
                msg.attach(text_part)

            # إضافة المحتوى HTML
            if rendered_content['html_content']:
                html_part = MIMEText(rendered_content['html_content'], 'html', 'utf-8')
                msg.attach(html_part)

            # إرسال الرسالة باستخدام SMTP مخصص
            success = self._send_via_smtp(msg, recipient.email)

            # تسجيل النتيجة
            EmailLog.objects.create(
                recipient=recipient,
                subject=rendered_content['subject'],
                status='sent' if success else 'failed',
                sent_at=timezone.now(),
                error_message='' if success else 'فشل في الإرسال'
            )

            return success

        except Exception as e:
            # تسجيل الخطأ
            logger.error(f"خطأ في إرسال البريد إلى {recipient.email}: {str(e)}")

            # تسجيل الخطأ في قاعدة البيانات
            EmailLog.objects.create(
                recipient=recipient,
                subject=rendered_content['subject'],
                status='failed',
                sent_at=timezone.now(),
                error_message=str(e)
            )

            return False

    def _send_via_smtp(self, msg, recipient_email):
        """إرسال الرسالة باستخدام SMTP مخصص"""
        try:
            # إنشاء اتصال SMTP
            if self.email_settings.use_ssl:
                server = smtplib.SMTP_SSL(
                    self.email_settings.smtp_host,
                    self.email_settings.smtp_port,
                    timeout=30
                )
            else:
                server = smtplib.SMTP(
                    self.email_settings.smtp_host,
                    self.email_settings.smtp_port,
                    timeout=30
                )

                if self.email_settings.use_tls:
                    server.starttls()

            # تسجيل الدخول
            server.login(
                self.email_settings.smtp_username,
                self.email_settings.decrypt_password()
            )

            # إرسال الرسالة
            server.send_message(msg)
            server.quit()

            logger.info(f"تم إرسال البريد بنجاح إلى {recipient_email}")
            return True

        except Exception as e:
            logger.error(f"خطأ في إرسال البريد عبر SMTP إلى {recipient_email}: {str(e)}")
            return False
    
    def _queue_email(self, recipient, template, rendered_content, context, delay_minutes=0):
        """إضافة البريد إلى طابور الإرسال"""
        try:
            scheduled_at = timezone.now()
            if delay_minutes > 0:
                from datetime import timedelta
                scheduled_at += timedelta(minutes=delay_minutes)
            
            EmailQueue.objects.create(
                recipient=recipient,
                template=template,
                subject=rendered_content['subject'],
                context_data=context or {},
                scheduled_at=scheduled_at,
                status='pending'
            )
            
            logger.info(f"تم إضافة البريد إلى الطابور للمستخدم {recipient.email}")
            return True
            
        except Exception as e:
            logger.error(f"خطأ في إضافة البريد للطابور: {str(e)}")
            return False
    
    def process_queue(self, limit=50):
        """معالجة طابور البريد الإلكتروني"""
        try:
            # الحصول على الرسائل المعلقة
            pending_emails = EmailQueue.objects.filter(
                status='pending',
                scheduled_at__lte=timezone.now()
            ).order_by('scheduled_at')[:limit]
            
            sent_count = 0
            failed_count = 0
            
            for email_item in pending_emails:
                try:
                    # تحديث الحالة
                    email_item.status = 'sending'
                    email_item.save()
                    
                    # عرض القالب
                    rendered = self.render_template(email_item.template, email_item.context_data)
                    if not rendered:
                        email_item.status = 'failed'
                        email_item.error_message = 'فشل في عرض القالب'
                        email_item.attempts += 1
                        email_item.save()
                        failed_count += 1
                        continue
                    
                    # إرسال البريد
                    success = self._send_immediate(email_item.recipient, rendered)
                    
                    if success:
                        email_item.status = 'sent'
                        sent_count += 1
                    else:
                        email_item.status = 'failed'
                        email_item.attempts += 1
                        failed_count += 1
                    
                    email_item.save()
                    
                except Exception as e:
                    email_item.status = 'failed'
                    email_item.error_message = str(e)
                    email_item.attempts += 1
                    email_item.save()
                    failed_count += 1
                    logger.error(f"خطأ في معالجة البريد {email_item.id}: {str(e)}")
            
            logger.info(f"تمت معالجة الطابور: {sent_count} نجح، {failed_count} فشل")
            return {'sent': sent_count, 'failed': failed_count}
            
        except Exception as e:
            logger.error(f"خطأ في معالجة طابور البريد: {str(e)}")
            return {'sent': 0, 'failed': 0}
    
    def test_connection(self):
        """اختبار الاتصال بخادم SMTP"""
        try:
            if not self.email_settings:
                return False, "لا توجد إعدادات بريد إلكتروني"
            
            # اختبار الاتصال
            if self.email_settings.use_ssl:
                server = smtplib.SMTP_SSL(self.email_settings.smtp_host, self.email_settings.smtp_port)
            else:
                server = smtplib.SMTP(self.email_settings.smtp_host, self.email_settings.smtp_port)
                if self.email_settings.use_tls:
                    server.starttls()
            
            # تسجيل الدخول
            password = self.email_settings.decrypt_password()
            server.login(self.email_settings.smtp_username, password)
            server.quit()
            
            return True, "تم الاتصال بنجاح"
            
        except Exception as e:
            return False, f"فشل الاتصال: {str(e)}"
    
    def get_statistics(self):
        """الحصول على إحصائيات البريد الإلكتروني"""
        try:
            today = timezone.now().date()
            
            # إحصائيات اليوم
            today_sent = EmailLog.objects.filter(
                sent_at__date=today,
                status='sent'
            ).count()
            
            today_failed = EmailLog.objects.filter(
                sent_at__date=today,
                status='failed'
            ).count()
            
            # طابور الانتظار
            pending_queue = EmailQueue.objects.filter(status='pending').count()
            
            # إجمالي الرسائل
            total_sent = EmailLog.objects.filter(status='sent').count()
            total_failed = EmailLog.objects.filter(status='failed').count()
            
            return {
                'today_sent': today_sent,
                'today_failed': today_failed,
                'pending_queue': pending_queue,
                'total_sent': total_sent,
                'total_failed': total_failed,
                'success_rate': round((total_sent / (total_sent + total_failed) * 100), 2) if (total_sent + total_failed) > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"خطأ في الحصول على الإحصائيات: {str(e)}")
            return {
                'today_sent': 0,
                'today_failed': 0,
                'pending_queue': 0,
                'total_sent': 0,
                'total_failed': 0,
                'success_rate': 0
            }
