{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/enhanced-breadcrumb.css' %}">
<style>
    .template-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .template-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .template-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 1.5rem;
    }

    .template-body {
        padding: 1.5rem;
    }

    .template-type-badge {
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .template-type-welcome {
        background: #e3f2fd;
        color: #1976d2;
    }

    .template-type-lesson_reminder {
        background: #f3e5f5;
        color: #7b1fa2;
    }

    .template-type-subscription_expiry {
        background: #fff3e0;
        color: #f57c00;
    }

    .template-type-admin_report {
        background: #e8f5e8;
        color: #388e3c;
    }

    .template-type-general {
        background: #f5f5f5;
        color: #616161;
    }

    .template-type-payment {
        background: #fce4ec;
        color: #c2185b;
    }

    .template-type-system {
        background: #ffebee;
        color: #d32f2f;
    }

    .search-box {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }

    .btn-create {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-edit {
        background: #667eea;
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        font-size: 0.9rem;
    }

    .btn-edit:hover {
        background: #5a6fd8;
        color: white;
    }

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-left: 8px;
    }

    .status-active {
        background-color: #28a745;
    }

    .status-inactive {
        background-color: #dc3545;
    }

    .default-badge {
        background: #ffd700;
        color: #856404;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: bold;
    }

    /* تحسين مسارات التوجيه */
    .breadcrumb {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    .breadcrumb-item {
        color: #6c757d;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .breadcrumb-item a {
        color: #11998e;
        text-decoration: none;
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
        transition: all 0.3s ease;
        display: inline-block;
    }

    .breadcrumb-item a:hover {
        background: rgba(17, 153, 142, 0.1);
        color: #0d7377;
        transform: translateY(-1px);
    }

    .breadcrumb-item.active {
        color: #2D5016;
        font-weight: 600;
        background: rgba(45, 80, 22, 0.1);
        padding: 0.5rem 0.75rem;
        border-radius: 8px;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "←";
        color: #11998e;
        font-weight: bold;
        margin: 0 0.5rem;
    }

    .page-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
    }

    @media (max-width: 768px) {
        .template-card {
            margin-bottom: 1rem;
        }
        
        .template-header {
            padding: 1rem;
        }
        
        .template-body {
            padding: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="{% url 'admin_dashboard' %}">
                    <i class="fas fa-home me-2"></i>لوحة التحكم
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'technical_settings' %}">
                    <i class="fas fa-cogs me-2"></i>الإعدادات التقنية
                </a>
            </li>
            <li class="breadcrumb-item">
                <a href="{% url 'email_settings_dashboard' %}">
                    <i class="fas fa-envelope-open-text me-2"></i>إعدادات البريد
                </a>
            </li>
            <li class="breadcrumb-item active">
                <i class="fas fa-file-alt me-2"></i>قوالب البريد الإلكتروني
            </li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="page-header text-center">
        <div class="container">
            <h1 class="mb-3">
                <i class="fas fa-file-alt me-3"></i>
                قوالب البريد الإلكتروني
            </h1>
            <p class="lead mb-0">إنشاء وإدارة قوالب الإشعارات مع المتغيرات الديناميكية</p>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="search-box">
        <form method="get" class="row align-items-end">
            <div class="col-md-4">
                <label for="search" class="form-label">البحث في القوالب</label>
                <input type="text" class="form-control" id="search" name="search" 
                       value="{{ search_query }}" placeholder="اسم القالب أو الموضوع">
            </div>
            <div class="col-md-3">
                <label for="type" class="form-label">نوع القالب</label>
                <select class="form-select" id="type" name="type">
                    <option value="">جميع الأنواع</option>
                    {% for value, label in template_types %}
                        <option value="{{ value }}" {% if template_type == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>بحث
                </button>
                <a href="{% url 'email_templates_list' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-2"></i>مسح
                </a>
            </div>
            <div class="col-md-2 text-end">
                <a href="{% url 'email_template_create' %}" class="btn btn-create">
                    <i class="fas fa-plus me-2"></i>قالب جديد
                </a>
            </div>
        </form>
    </div>

    <!-- Templates Grid -->
    <div class="row">
        {% for template in page_obj %}
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="template-card">
                    <div class="template-header">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h5 class="mb-1">{{ template.name }}</h5>
                                <span class="template-type-badge template-type-{{ template.template_type }}">
                                    {{ template.get_template_type_display }}
                                </span>
                            </div>
                            <div class="text-end">
                                {% if template.is_active %}
                                    <span class="status-indicator status-active"></span>
                                {% else %}
                                    <span class="status-indicator status-inactive"></span>
                                {% endif %}
                                {% if template.is_default %}
                                    <br><span class="default-badge mt-1">افتراضي</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="template-body">
                        <h6 class="text-muted mb-2">موضوع الرسالة:</h6>
                        <p class="mb-3">{{ template.subject|truncatechars:60 }}</p>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                {{ template.created_at|date:"d/m/Y" }}
                            </small>
                            <div>
                                <a href="{% url 'email_template_edit' template.id %}" class="btn btn-edit btn-sm">
                                    <i class="fas fa-edit me-1"></i>تعديل
                                </a>
                                <button class="btn btn-outline-info btn-sm" onclick="previewTemplate({{ template.id }})">
                                    <i class="fas fa-eye me-1"></i>معاينة
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-5x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد قوالب</h4>
                    <p class="text-muted">لم يتم العثور على قوالب تطابق معايير البحث</p>
                    <a href="{% url 'email_template_create' %}" class="btn btn-create">
                        <i class="fas fa-plus me-2"></i>إنشاء أول قالب
                    </a>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if template_type %}&type={{ template_type }}{% endif %}">
                            السابق
                        </a>
                    </li>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if template_type %}&type={{ template_type }}{% endif %}">
                                {{ num }}
                            </a>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if template_type %}&type={{ template_type }}{% endif %}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% endif %}
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة القالب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function previewTemplate(templateId) {
    // فتح المودال
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    const content = document.getElementById('previewContent');
    
    // إظهار تحميل
    content.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p class="mt-2">جاري التحميل...</p></div>';
    modal.show();
    
    // تحميل المعاينة (سيتم إضافة API لاحقاً)
    setTimeout(() => {
        content.innerHTML = '<div class="alert alert-info">معاينة القالب ستكون متاحة قريباً</div>';
    }, 1000);
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثيرات hover للبطاقات
    const cards = document.querySelectorAll('.template-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
{% endblock %}
