{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .email-settings-card {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border-radius: 20px;
        padding: 2rem;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        min-height: 200px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .email-settings-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }

    .email-settings-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .email-settings-card:hover::before {
        opacity: 1;
    }

    .card-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.9;
    }

    .card-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .card-description {
        font-size: 0.9rem;
        opacity: 0.9;
        line-height: 1.4;
    }

    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        color: #2D5016;
    }

    .stats-label {
        color: #666;
        font-size: 0.9rem;
    }

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-left: 8px;
    }

    .status-active {
        background-color: #28a745;
    }

    .status-inactive {
        background-color: #dc3545;
    }

    .recent-logs {
        max-height: 400px;
        overflow-y: auto;
    }

    .log-item {
        padding: 0.75rem;
        border-bottom: 1px solid #eee;
        transition: background-color 0.2s ease;
    }

    .log-item:hover {
        background-color: #f8f9fa;
    }

    .log-status {
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: bold;
    }

    .log-status.sent {
        background-color: #d4edda;
        color: #155724;
    }

    .log-status.failed {
        background-color: #f8d7da;
        color: #721c24;
    }

    .breadcrumb-item {
        color: #6c757d;
    }

    .breadcrumb-item.active {
        color: #495057;
    }

    .page-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
    }

    .settings-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    @media (max-width: 768px) {
        .settings-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .email-settings-card {
            min-height: 150px;
            padding: 1.5rem;
        }

        .card-icon {
            font-size: 2.5rem;
        }

        .card-title {
            font-size: 1.3rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="{% url 'admin_technical_settings' %}">الإعدادات التقنية</a></li>
            <li class="breadcrumb-item active">إعدادات البريد الإلكتروني</li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="page-header text-center">
        <div class="container">
            <h1 class="mb-3">
                <i class="fas fa-envelope-open-text me-3"></i>
                إعدادات إشعارات البريد الإلكتروني
            </h1>
            <p class="lead mb-0">إدارة وتخصيص نظام إشعارات البريد الإلكتروني للأكاديمية</p>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number text-success">{{ stats.today_sent }}</div>
                <div class="stats-label">رسائل اليوم</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number text-danger">{{ stats.today_failed }}</div>
                <div class="stats-label">فشل اليوم</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number text-warning">{{ stats.pending_queue }}</div>
                <div class="stats-label">في الانتظار</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stats-card text-center">
                <div class="stats-number text-info">{{ stats.success_rate }}%</div>
                <div class="stats-label">معدل النجاح</div>
            </div>
        </div>
    </div>

    <!-- Settings Cards -->
    <div class="settings-grid">
        <!-- إعدادات SMTP -->
        <div class="email-settings-card" onclick="window.location.href='{% url 'email_smtp_settings' %}'">
            <div>
                <div class="card-icon">
                    <i class="fas fa-server"></i>
                </div>
                <div class="card-title">إعدادات SMTP</div>
                <div class="card-description">
                    تكوين خادم البريد الإلكتروني ومزودي الخدمة
                    {% if email_settings %}
                        <span class="status-indicator status-active"></span>
                        مكون
                    {% else %}
                        <span class="status-indicator status-inactive"></span>
                        غير مكون
                    {% endif %}
                </div>
            </div>
            <div class="mt-3">
                <i class="fas fa-arrow-left opacity-75"></i>
            </div>
        </div>

        <!-- إدارة القوالب -->
        <div class="email-settings-card" onclick="window.location.href='{% url 'email_templates_list' %}'">
            <div>
                <div class="card-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="card-title">إدارة القوالب</div>
                <div class="card-description">
                    إنشاء وتعديل قوالب البريد الإلكتروني
                    <br>{{ templates_count }} قالب نشط
                </div>
            </div>
            <div class="mt-3">
                <i class="fas fa-arrow-left opacity-75"></i>
            </div>
        </div>

        <!-- إعدادات الأحداث -->
        <div class="email-settings-card" onclick="window.location.href='{% url 'email_events_list' %}'">
            <div>
                <div class="card-icon">
                    <i class="fas fa-bell"></i>
                </div>
                <div class="card-title">إعدادات الأحداث</div>
                <div class="card-description">
                    تخصيص أحداث الإشعارات وتوقيت الإرسال
                    <br>{{ events_count }} حدث نشط
                </div>
            </div>
            <div class="mt-3">
                <i class="fas fa-arrow-left opacity-75"></i>
            </div>
        </div>

        <!-- سجلات الإرسال -->
        <div class="email-settings-card" onclick="window.location.href='{% url 'email_logs_list' %}'">
            <div>
                <div class="card-icon">
                    <i class="fas fa-history"></i>
                </div>
                <div class="card-title">سجلات الإرسال</div>
                <div class="card-description">
                    عرض سجلات وإحصائيات إرسال البريد الإلكتروني
                    <br>{{ stats.total_sent }} رسالة مرسلة
                </div>
            </div>
            <div class="mt-3">
                <i class="fas fa-arrow-left opacity-75"></i>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        آخر الرسائل المرسلة
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="recent-logs">
                        {% if recent_logs %}
                            {% for log in recent_logs %}
                                <div class="log-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong>{{ log.recipient.get_full_name|default:log.recipient.email }}</strong>
                                            <br>
                                            <small class="text-muted">{{ log.subject }}</small>
                                        </div>
                                        <div class="text-end">
                                            <span class="log-status {{ log.status }}">
                                                {% if log.status == 'sent' %}
                                                    <i class="fas fa-check"></i> تم الإرسال
                                                {% elif log.status == 'failed' %}
                                                    <i class="fas fa-times"></i> فشل
                                                {% endif %}
                                            </span>
                                            <br>
                                            <small class="text-muted">{{ log.sent_at|timesince }} مضت</small>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">لا توجد رسائل مرسلة بعد</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
                {% if recent_logs %}
                    <div class="card-footer text-center">
                        <a href="{% url 'email_logs_list' %}" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>
                            عرض جميع السجلات
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث الإحصائيات كل 30 ثانية
setInterval(function() {
    fetch('{% url "get_email_statistics" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث الإحصائيات
                document.querySelector('.stats-number.text-success').textContent = data.stats.today_sent;
                document.querySelector('.stats-number.text-danger').textContent = data.stats.today_failed;
                document.querySelector('.stats-number.text-warning').textContent = data.stats.pending_queue;
                document.querySelector('.stats-number.text-info').textContent = data.stats.success_rate + '%';
            }
        })
        .catch(error => console.error('Error updating statistics:', error));
}, 30000);

// إضافة تأثيرات hover للبطاقات
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.email-settings-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
{% endblock %}
