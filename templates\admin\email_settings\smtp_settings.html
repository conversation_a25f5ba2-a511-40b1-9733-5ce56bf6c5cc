{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .smtp-form-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .smtp-form-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-label {
        font-weight: 600;
        color: #2D5016;
        margin-bottom: 0.5rem;
    }

    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 0.75rem;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #11998e;
        box-shadow: 0 0 0 0.2rem rgba(17, 153, 142, 0.25);
    }

    .btn-test {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-test:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }

    .btn-save {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        border: none;
        color: white;
        padding: 0.75rem 2rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-save:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        color: white;
    }

    .provider-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 1rem;
        margin-top: 1rem;
        display: none;
    }

    .provider-info.active {
        display: block;
    }

    .test-result {
        margin-top: 1rem;
        padding: 1rem;
        border-radius: 8px;
        display: none;
    }

    .test-result.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .test-result.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .breadcrumb-item {
        color: #6c757d;
    }

    .breadcrumb-item.active {
        color: #495057;
    }

    .page-header {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
    }

    @media (max-width: 768px) {
        .smtp-form-header {
            padding: 1rem;
        }
        
        .btn-test, .btn-save {
            width: 100%;
            margin-bottom: 0.5rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'admin_dashboard' %}">لوحة التحكم</a></li>
            <li class="breadcrumb-item"><a href="{% url 'technical_settings' %}">الإعدادات التقنية</a></li>
            <li class="breadcrumb-item"><a href="{% url 'email_settings_dashboard' %}">إعدادات البريد</a></li>
            <li class="breadcrumb-item active">إعدادات SMTP</li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="page-header text-center">
        <div class="container">
            <h1 class="mb-3">
                <i class="fas fa-server me-3"></i>
                إعدادات خادم SMTP
            </h1>
            <p class="lead mb-0">تكوين خادم البريد الإلكتروني ومزودي الخدمة</p>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="smtp-form-card">
                <div class="smtp-form-header">
                    <h4 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات SMTP
                    </h4>
                </div>
                
                <div class="card-body p-4">
                    <form method="post" id="smtpForm">
                        {% csrf_token %}
                        
                        <!-- مزود الخدمة -->
                        <div class="form-group">
                            <label for="{{ form.provider.id_for_label }}" class="form-label">
                                <i class="fas fa-building me-2"></i>
                                {{ form.provider.label }}
                            </label>
                            {{ form.provider }}
                            {% if form.provider.errors %}
                                <div class="text-danger mt-1">{{ form.provider.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- معلومات المزودين -->
                        <div id="provider-gmail" class="provider-info">
                            <h6><i class="fab fa-google me-2"></i>إعدادات Gmail</h6>
                            <p><strong>خادم SMTP:</strong> smtp.gmail.com</p>
                            <p><strong>المنفذ:</strong> 587 (TLS) أو 465 (SSL)</p>
                            <p><strong>ملاحظة:</strong> استخدم كلمة مرور التطبيق بدلاً من كلمة المرور العادية</p>
                        </div>

                        <div id="provider-outlook" class="provider-info">
                            <h6><i class="fab fa-microsoft me-2"></i>إعدادات Outlook</h6>
                            <p><strong>خادم SMTP:</strong> smtp-mail.outlook.com</p>
                            <p><strong>المنفذ:</strong> 587</p>
                            <p><strong>التشفير:</strong> TLS</p>
                        </div>

                        <div id="provider-namecheap" class="provider-info">
                            <h6><i class="fas fa-envelope me-2"></i>إعدادات Namecheap</h6>
                            <p><strong>خادم SMTP:</strong> mail.privateemail.com</p>
                            <p><strong>المنفذ:</strong> 587 أو 465</p>
                            <p><strong>التشفير:</strong> TLS أو SSL</p>
                        </div>

                        <div class="row">
                            <!-- خادم SMTP -->
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="{{ form.smtp_host.id_for_label }}" class="form-label">
                                        <i class="fas fa-server me-2"></i>
                                        {{ form.smtp_host.label }}
                                    </label>
                                    {{ form.smtp_host }}
                                    {% if form.smtp_host.errors %}
                                        <div class="text-danger mt-1">{{ form.smtp_host.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- منفذ SMTP -->
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="{{ form.smtp_port.id_for_label }}" class="form-label">
                                        <i class="fas fa-plug me-2"></i>
                                        {{ form.smtp_port.label }}
                                    </label>
                                    {{ form.smtp_port }}
                                    {% if form.smtp_port.errors %}
                                        <div class="text-danger mt-1">{{ form.smtp_port.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- اسم المستخدم -->
                        <div class="form-group">
                            <label for="{{ form.smtp_username.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-2"></i>
                                {{ form.smtp_username.label }}
                            </label>
                            {{ form.smtp_username }}
                            {% if form.smtp_username.errors %}
                                <div class="text-danger mt-1">{{ form.smtp_username.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- كلمة المرور -->
                        <div class="form-group">
                            <label for="{{ form.smtp_password_plain.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2"></i>
                                {{ form.smtp_password_plain.label }}
                            </label>
                            {{ form.smtp_password_plain }}
                            {% if form.smtp_password_plain.errors %}
                                <div class="text-danger mt-1">{{ form.smtp_password_plain.errors.0 }}</div>
                            {% endif %}
                            {% if form.smtp_password_plain.help_text %}
                                <small class="form-text text-muted">{{ form.smtp_password_plain.help_text }}</small>
                            {% endif %}
                        </div>

                        <div class="row">
                            <!-- البريد المرسل -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.from_email.id_for_label }}" class="form-label">
                                        <i class="fas fa-envelope me-2"></i>
                                        {{ form.from_email.label }}
                                    </label>
                                    {{ form.from_email }}
                                    {% if form.from_email.errors %}
                                        <div class="text-danger mt-1">{{ form.from_email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- اسم المرسل -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.from_name.id_for_label }}" class="form-label">
                                        <i class="fas fa-signature me-2"></i>
                                        {{ form.from_name.label }}
                                    </label>
                                    {{ form.from_name }}
                                    {% if form.from_name.errors %}
                                        <div class="text-danger mt-1">{{ form.from_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات التشفير -->
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-check">
                                    {{ form.use_tls }}
                                    <label class="form-check-label" for="{{ form.use_tls.id_for_label }}">
                                        {{ form.use_tls.label }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    {{ form.use_ssl }}
                                    <label class="form-check-label" for="{{ form.use_ssl.id_for_label }}">
                                        {{ form.use_ssl.label }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        {{ form.is_active.label }}
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- الحد الأقصى للرسائل -->
                        <div class="form-group">
                            <label for="{{ form.max_emails_per_hour.id_for_label }}" class="form-label">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                {{ form.max_emails_per_hour.label }}
                            </label>
                            {{ form.max_emails_per_hour }}
                            {% if form.max_emails_per_hour.errors %}
                                <div class="text-danger mt-1">{{ form.max_emails_per_hour.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <!-- أزرار العمل -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <button type="button" class="btn btn-test" onclick="testConnection()">
                                <i class="fas fa-plug me-2"></i>
                                اختبار الاتصال
                            </button>
                            
                            <button type="submit" class="btn btn-save">
                                <i class="fas fa-save me-2"></i>
                                حفظ الإعدادات
                            </button>
                        </div>

                        <!-- نتيجة الاختبار -->
                        <div id="testResult" class="test-result"></div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تحديث إعدادات SMTP حسب المزود
function updateSMTPSettings(provider) {
    // إخفاء جميع معلومات المزودين
    document.querySelectorAll('.provider-info').forEach(info => {
        info.classList.remove('active');
    });
    
    // إظهار معلومات المزود المحدد
    const providerInfo = document.getElementById('provider-' + provider);
    if (providerInfo) {
        providerInfo.classList.add('active');
    }
    
    // تحديث الإعدادات التلقائية
    const hostField = document.getElementById('{{ form.smtp_host.id_for_label }}');
    const portField = document.getElementById('{{ form.smtp_port.id_for_label }}');
    const tlsField = document.getElementById('{{ form.use_tls.id_for_label }}');
    const sslField = document.getElementById('{{ form.use_ssl.id_for_label }}');
    
    switch(provider) {
        case 'gmail':
            hostField.value = 'smtp.gmail.com';
            portField.value = '587';
            tlsField.checked = true;
            sslField.checked = false;
            break;
        case 'outlook':
            hostField.value = 'smtp-mail.outlook.com';
            portField.value = '587';
            tlsField.checked = true;
            sslField.checked = false;
            break;
        case 'yahoo':
            hostField.value = 'smtp.mail.yahoo.com';
            portField.value = '587';
            tlsField.checked = true;
            sslField.checked = false;
            break;
        case 'namecheap':
            hostField.value = 'mail.privateemail.com';
            portField.value = '587';
            tlsField.checked = true;
            sslField.checked = false;
            break;
        case 'hostinger':
            hostField.value = 'smtp.hostinger.com';
            portField.value = '587';
            tlsField.checked = true;
            sslField.checked = false;
            break;
        case 'godaddy':
            hostField.value = 'smtpout.secureserver.net';
            portField.value = '587';
            tlsField.checked = true;
            sslField.checked = false;
            break;
    }
}

// اختبار الاتصال
function testConnection() {
    const button = document.querySelector('.btn-test');
    const resultDiv = document.getElementById('testResult');
    
    // تعطيل الزر وإظهار التحميل
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';
    
    // إخفاء النتيجة السابقة
    resultDiv.style.display = 'none';
    
    fetch('{% url "test_smtp_connection" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        // إعادة تفعيل الزر
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-plug me-2"></i>اختبار الاتصال';
        
        // إظهار النتيجة
        resultDiv.style.display = 'block';
        if (data.success) {
            resultDiv.className = 'test-result success';
            resultDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>' + data.message;
        } else {
            resultDiv.className = 'test-result error';
            resultDiv.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>' + data.message;
        }
    })
    .catch(error => {
        // إعادة تفعيل الزر
        button.disabled = false;
        button.innerHTML = '<i class="fas fa-plug me-2"></i>اختبار الاتصال';
        
        // إظهار خطأ
        resultDiv.style.display = 'block';
        resultDiv.className = 'test-result error';
        resultDiv.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>حدث خطأ في الاختبار';
        console.error('Error:', error);
    });
}

// تحديث الإعدادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    const providerSelect = document.getElementById('{{ form.provider.id_for_label }}');
    if (providerSelect.value) {
        updateSMTPSettings(providerSelect.value);
    }
});
</script>
{% endblock %}
