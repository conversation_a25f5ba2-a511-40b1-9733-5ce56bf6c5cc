{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
    .settings-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 2rem;
        color: white;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        min-height: 200px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }

    .settings-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }

    .settings-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .settings-card:hover::before {
        opacity: 1;
    }

    .card-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.9;
    }

    .card-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .card-description {
        font-size: 0.95rem;
        opacity: 0.9;
        line-height: 1.5;
    }



    .notifications-card {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }

    .payments-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }



    .info-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        cursor: default;
        min-height: 120px;
        padding: 1.5rem;
    }

    .info-card:hover {
        transform: none;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .info-card .card-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .info-card .card-title {
        font-size: 1.2rem;
        margin-bottom: 0.3rem;
    }

    .info-card .card-description {
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .breadcrumb-item {
        color: #6c757d;
    }

    .breadcrumb-item.active {
        color: #495057;
    }

    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
    }

    .settings-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        margin-top: 2rem;
    }

    @media (max-width: 1200px) {
        .settings-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 768px) {
        .settings-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .settings-card {
            min-height: 150px;
            padding: 1.5rem;
        }

        .card-icon {
            font-size: 2.5rem;
        }

        .card-title {
            font-size: 1.3rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Page Header -->
    <div class="page-header text-center">
        <h1 class="mb-3">
            <i class="fas fa-cogs me-3"></i>
            الإعدادات التقنية
        </h1>
        <p class="lead mb-0">إدارة الإعدادات التقنية والتكنولوجية للمنصة</p>
    </div>

    <!-- Info Section - Fixed Above Cards -->
    <div class="mb-4">
        <div class="settings-card info-card">
            <div>
                <div class="card-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="card-title">معلومات مهمة</div>
                <div class="card-description">
                    هذه الصفحة تحتوي على الإعدادات التقنية الحساسة للمنصة. يرجى التأكد من صحة الإعدادات قبل حفظها.
                    في حالة وجود مشاكل تقنية، يرجى التواصل مع فريق الدعم الفني.
                </div>
            </div>
            <div class="mt-3">
                <i class="fas fa-exclamation-triangle opacity-75"></i>
            </div>
        </div>
    </div>

    <!-- Settings Cards -->
    <div class="settings-grid">


        <!-- إعدادات بوابات الدفع -->
        <div class="settings-card payments-card" onclick="window.location.href='/dashboard/admin/payment-gateway-settings/'">
            <div>
                <div class="card-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="card-title">إعدادات بوابات الدفع</div>
                <div class="card-description">
                    إعداد وإدارة بوابات الدفع المختلفة مثل PayPal، Stripe، والتحويل البنكي.
                </div>
            </div>
            <div class="mt-3">
                <i class="fas fa-arrow-left opacity-75"></i>
            </div>
        </div>

        <!-- إعدادات الأكاديمية -->
        <div class="settings-card academy-card" onclick="window.location.href='/dashboard/admin/academy-settings/'">
            <div>
                <div class="card-icon">
                    <i class="fas fa-university"></i>
                </div>
                <div class="card-title">إعدادات الأكاديمية</div>
                <div class="card-description">
                    إعداد معلومات الأكاديمية الأساسية مثل الاسم والشعار ومعلومات الاتصال.
                </div>
            </div>
            <div class="mt-3">
                <i class="fas fa-arrow-left opacity-75"></i>
            </div>
        </div>


    </div>


</div>



{% endblock %}

{% block extra_js %}
<script>
function showComingSoon() {
    Swal.fire({
        title: 'قريباً!',
        text: 'هذه الميزة ستكون متاحة قريباً',
        icon: 'info',
        confirmButtonText: 'حسناً',
        confirmButtonColor: '#667eea'
    });
}

// Add hover effects
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.settings-card:not(.info-card)');
    console.log('Found', cards.length, 'settings cards');

    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
{% endblock %}

function forceOpenModal() {
    console.log('Force opening modal...');
    const modal = document.getElementById('whatsappModal');
    if (modal) {
        modal.style.display = 'block';
        modal.classList.add('show');
        modal.style.paddingRight = '17px';

        // إضافة backdrop
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop fade show';
        backdrop.id = 'force-backdrop';
        document.body.appendChild(backdrop);

        document.body.classList.add('modal-open');
        document.body.style.overflow = 'hidden';

        console.log('Modal opened successfully!');
    } else {
        alert('خطأ: لا يمكن العثور على نافذة الإعدادات');
    }
}

function forceCloseModal() {
    console.log('Force closing modal...');
    const modal = document.getElementById('whatsappModal');
    if (modal) {
        modal.style.display = 'none';
        modal.classList.remove('show');
        modal.style.paddingRight = '';

        // إزالة backdrop
        const backdrop = document.getElementById('force-backdrop');
        if (backdrop) {
            backdrop.remove();
        }

        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';

        console.log('Modal closed successfully!');
    }
}

function openWhatsAppModal() {
    console.log('Opening WhatsApp modal...');

    const modal = document.getElementById('whatsappModal');
    if (modal) {
        // جرب Bootstrap أولاً
        if (typeof $ !== 'undefined' && $.fn.modal) {
            console.log('Using Bootstrap modal');
            $('#whatsappModal').modal('show');
        } else {
            // استخدم طريقة مباشرة
            console.log('Using direct modal display');
            modal.style.display = 'block';
            modal.classList.add('show');
            modal.setAttribute('aria-hidden', 'false');

            // إضافة backdrop
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            backdrop.id = 'whatsapp-modal-backdrop';
            document.body.appendChild(backdrop);

            // منع scroll في الـ body
            document.body.style.overflow = 'hidden';
        }

        loadTemplates();
        loadRecentMessages();
    } else {
        console.error('Modal not found!');
        alert('خطأ: لا يمكن فتح نافذة إعدادات WhatsApp');
    }
}

function closeWhatsAppModal() {
    console.log('Closing WhatsApp modal...');

    const modal = document.getElementById('whatsappModal');
    if (modal) {
        if (typeof $ !== 'undefined' && $.fn.modal) {
            $('#whatsappModal').modal('hide');
        } else {
            modal.style.display = 'none';
            modal.classList.remove('show');
            modal.setAttribute('aria-hidden', 'true');

            // إزالة backdrop
            const backdrop = document.getElementById('whatsapp-modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }

            // إعادة scroll للـ body
            document.body.style.overflow = '';
        }
    }
}

function saveSettings() {
    const form = document.getElementById('whatsappSettingsForm');
    const formData = new FormData(form);

    // إضافة قيم checkbox
    formData.set('enabled', document.getElementById('enabled').checked);
    formData.set('auto_lesson_reminders', document.getElementById('auto_lesson_reminders').checked);
    formData.set('auto_subscription_notifications', document.getElementById('auto_subscription_notifications').checked);

    fetch('/dashboard/admin/whatsapp/save-settings/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                title: 'تم الحفظ!',
                text: data.message,
                icon: 'success',
                confirmButtonText: 'حسناً'
            });

            // تحديث حالة البطاقة
            updateCardStatus(data.is_configured);

        } else {
            Swal.fire({
                title: 'خطأ!',
                text: data.error,
                icon: 'error',
                confirmButtonText: 'حسناً'
            });
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire({
            title: 'خطأ!',
            text: 'حدث خطأ غير متوقع',
            icon: 'error',
            confirmButtonText: 'حسناً'
        });
    });
}

function testConnection() {
    const statusDiv = document.getElementById('connection-status');
    statusDiv.className = 'alert alert-info';
    statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري اختبار الاتصال...';
    statusDiv.classList.remove('d-none');

    fetch('/dashboard/admin/whatsapp/test-connection/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusDiv.className = 'alert alert-success';
            statusDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>' + data.message;
        } else {
            statusDiv.className = 'alert alert-danger';
            statusDiv.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>' + data.message;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        statusDiv.className = 'alert alert-danger';
        statusDiv.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>حدث خطأ في الاتصال';
    });
}

function sendTestMessage() {
    const phone = document.getElementById('test_phone').value;
    const template = document.getElementById('test_template').value;
    const resultDiv = document.getElementById('test-result');

    if (!phone) {
        Swal.fire({
            title: 'خطأ!',
            text: 'يرجى إدخال رقم الهاتف',
            icon: 'error',
            confirmButtonText: 'حسناً'
        });
        return;
    }

    resultDiv.className = 'alert alert-info mt-3';
    resultDiv.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري إرسال الرسالة...';

    const formData = new FormData();
    formData.append('phone', phone);
    formData.append('template_name', template);

    fetch('/dashboard/admin/whatsapp/send-test/', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.className = 'alert alert-success mt-3';
            resultDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>' + data.message;

            // تحديث الإحصائيات
            updateStats();

        } else {
            resultDiv.className = 'alert alert-danger mt-3';
            resultDiv.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>' + data.message;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultDiv.className = 'alert alert-danger mt-3';
        resultDiv.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i>حدث خطأ في الإرسال';
    });
}

function loadTemplates() {
    fetch('/dashboard/admin/whatsapp/get-templates/')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayTemplates(data.templates);
        }
    })
    .catch(error => console.error('Error loading templates:', error));
}

function displayTemplates(templates) {
    const container = document.getElementById('templates-list');

    if (templates.length === 0) {
        container.innerHTML = '<div class="alert alert-warning">لا توجد قوالب متاحة</div>';
        return;
    }

    let html = '';
    templates.forEach(template => {
        const variablesText = Object.entries(template.variables)
            .map(([key, value]) => `${key}: ${value}`)
            .join(', ');

        html += `
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6 class="card-title">${template.display_name}</h6>
                            <p class="card-text text-muted">${template.description}</p>
                            <small class="text-muted">
                                <strong>اسم القالب:</strong> ${template.name}<br>
                                <strong>المتغيرات:</strong> ${variablesText}
                            </small>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-primary">${template.template_type}</span><br>
                            <small class="text-muted">استخدم ${template.usage_count} مرة</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

function loadRecentMessages() {
    fetch('/dashboard/admin/whatsapp/get-messages/')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayRecentMessages(data.messages);
        }
    })
    .catch(error => console.error('Error loading messages:', error));
}

function displayRecentMessages(messages) {
    const container = document.getElementById('recent-messages');

    if (messages.length === 0) {
        container.innerHTML = '<div class="alert alert-info">لا توجد رسائل</div>';
        return;
    }

    let html = '<div class="table-responsive"><table class="table table-sm">';
    html += '<thead><tr><th>المستقبل</th><th>القالب</th><th>الحالة</th><th>التاريخ</th></tr></thead><tbody>';

    messages.forEach(message => {
        const statusClass = message.status === 'sent' ? 'success' :
                           message.status === 'failed' ? 'danger' : 'warning';
        const date = new Date(message.created_at).toLocaleString('ar-SA');

        html += `
            <tr>
                <td>${message.recipient_name}<br><small class="text-muted">${message.recipient_phone}</small></td>
                <td>${message.template_name}</td>
                <td><span class="badge bg-${statusClass}">${message.status}</span></td>
                <td>${date}</td>
            </tr>
        `;
    });

    html += '</tbody></table></div>';
    container.innerHTML = html;
}

function updateStats() {
    fetch('/dashboard/admin/whatsapp/get-stats/')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const stats = data.stats;

            // تحديث إحصائيات البطاقة
            document.getElementById('messages-today').textContent = stats.daily_sent;
            document.getElementById('pending-scheduled').textContent = stats.pending_scheduled;

            // تحديث إحصائيات Modal
            document.getElementById('stat-daily-sent').textContent = stats.daily_sent;
            document.getElementById('stat-pending').textContent = stats.pending_scheduled;
            document.getElementById('stat-failed').textContent = stats.failed_today;
        }
    })
    .catch(error => console.error('Error updating stats:', error));
}

function updateCardStatus(isConfigured) {
    const indicator = document.getElementById('whatsapp-status-indicator');
    const statusText = document.getElementById('whatsapp-status-text');
    const enabled = document.getElementById('enabled').checked;

    if (enabled && isConfigured) {
        indicator.innerHTML = '<i class="fas fa-circle text-success"></i>';
        statusText.textContent = 'نشط ومكون';
    } else if (enabled) {
        indicator.innerHTML = '<i class="fas fa-circle text-warning"></i>';
        statusText.textContent = 'نشط - يحتاج إعداد';
    } else {
        indicator.innerHTML = '<i class="fas fa-circle text-secondary"></i>';
        statusText.textContent = 'معطل';
    }
}

function refreshTemplates() {
    loadTemplates();
}

function refreshMessages() {
    loadRecentMessages();
}

// Add hover effects
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing WhatsApp card...');

    // التحقق من وجود jQuery و Bootstrap
    if (typeof $ === 'undefined') {
        console.error('jQuery is not loaded!');
    } else {
        console.log('jQuery is loaded');
    }

    if (typeof bootstrap === 'undefined') {
        console.error('Bootstrap is not loaded!');
    } else {
        console.log('Bootstrap is loaded');
    }

    // التحقق من وجود العناصر
    const whatsappCard = document.getElementById('whatsapp-card');
    const whatsappModal = document.getElementById('whatsappModal');

    if (whatsappCard) {
        console.log('WhatsApp card found');
    } else {
        console.error('WhatsApp card not found!');
    }

    if (whatsappModal) {
        console.log('WhatsApp modal found');
    } else {
        console.error('WhatsApp modal not found!');
    }

    // إضافة event listener للزر مباشرة
    const whatsappBtn = document.getElementById('whatsapp-manage-btn');
    if (whatsappBtn) {
        console.log('WhatsApp button found, adding click listener');
        whatsappBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('WhatsApp button clicked!');
            openWhatsAppModal();
        });
    } else {
        console.error('WhatsApp button not found!');
    }

    // إضافة event listener لإغلاق الـ Modal عند الضغط خارجه
    if (whatsappModal) {
        whatsappModal.addEventListener('click', function(e) {
            if (e.target === whatsappModal) {
                closeWhatsAppModal();
            }
        });
    }

    // إضافة event listener لمفتاح Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const modal = document.getElementById('whatsappModal');
            if (modal && modal.classList.contains('show')) {
                closeWhatsAppModal();
            }
        }
    });

    const cards = document.querySelectorAll('.settings-card:not(.info-card)');
    console.log('Found', cards.length, 'settings cards');

    cards.forEach(card => {
        if (!card.id || card.id !== 'whatsapp-card') {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        }
    });

    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateStats, 30000);
});
</script>
{% endblock %}
